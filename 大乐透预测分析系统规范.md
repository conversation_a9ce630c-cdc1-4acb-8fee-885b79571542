# 大乐透预测分析系统规范文档

## 项目概述
基于历史开奖数据的大乐透预测分析系统，采用双轨并行验证架构，严格遵循时间约束原则。

## 核心原则

### 1. 严格时间约束（考试规则）
- **绝对禁止作弊**：预测第N期时只能使用第1到N-1期数据
- **时间线验证**：系统自动检查数据时间戳，拒绝任何违反时间约束的操作
- **真实性模拟**：每次预测都完全模拟真实购彩场景
- **数据隔离**：测试集完全封存，预测过程中不可见

### 2. 双轨并行架构
- **机器学习轨道**：使用算法自动学习数据模式
- **数学建模轨道**：基于统计理论建立数学模型
- **完全隔离**：两套系统独立运行，不相互影响
- **共享特征**：使用相同的统计分析基础

### 3. 回测验证目标
- 验证分析方法的真实有效性
- 量化预测能力和准确率
- 对比不同方法的优劣
- 评估实际应用的风险和收益

## 系统架构

### 数据层
```
历史开奖数据 → 时间戳验证 → 训练集/测试集分割
```

### 特征工程层（共享基础）
1. **冷热号统计** - 各号码历史出现频率
2. **大小号分析** - 大号(≥18)小号(<18)分布
3. **奇偶分析** - 奇偶号码比例统计
4. **和值分析** - 开奖号码总和分布
5. **连号分析** - 连续号码出现模式
6. **间隔分析** - 号码间距离分布
7. **区间分析** - 前区后区分布规律
8. **周期性分析** - 时间相关特征
9. **遗漏走势分析** - 号码遗漏期数和走势方向
10. **跨度分析** - 开奖号码最大值与最小值差值分布

### 双轨预测层

#### 机器学习轨道
- **算法选择**：神经网络、随机森林、支持向量机
- **特点**：自动学习复杂模式，黑盒模型
- **输出**：号码预测概率分布

#### 数学建模轨道  
- **方法选择**：概率分布、马尔可夫链、统计推断
- **特点**：基于数学理论，白盒模型，可解释
- **输出**：基于统计理论的预测结果

### 验证层
- **时间序列回测**：递进式验证，严格按时间顺序
- **准确率评估**：多维度指标体系
- **对比分析**：双轨结果对比，随机基准对比

## 回测验证流程

### 递进式回测
```
假设总数据1000期，保留最新20期做测试：

第1次：用第1-980期 → 预测第981期 → 验证
第2次：用第1-981期 → 预测第982期 → 验证  
...
第20次：用第1-999期 → 预测第1000期 → 验证
```

### 评估指标
1. **完全命中率** - 预测号码完全正确的概率
2. **部分命中率** - 预测中3个、4个、5个号码的概率  
3. **覆盖率** - 预测范围包含实际号码的比例
4. **投资回报率** - 考虑购买成本的收益分析
5. **一致性指标** - 预测结果的稳定性

## 防作弊机制

### 技术保障
1. **时间戳检查** - 自动验证数据使用的合法性
2. **代码锁定** - 预测算法提前确定，不可临时修改
3. **参数固定** - 模型参数基于训练集确定，不针对测试集调优
4. **过程审计** - 整个预测过程可重现、可验证

### 流程保障
1. **盲测环境** - 测试集答案完全隐藏
2. **模块化设计** - 每个功能独立测试验证
3. **严格测试** - 每个模块完成后必须通过反作弊测试

## 开发要求

### 模块化原则
- 每个功能模块独立开发
- 接口清晰，职责单一
- 便于测试和维护

### 质量要求
- 每个模块完成后必须严格测试
- 不能存在任何作弊可能
- 不能有不合理的逻辑漏洞
- 代码必须可审计、可重现

### 文档要求
- 详细的设计文档
- 完整的测试报告
- 清晰的使用说明

## 特征分析详细说明

### 1. 冷热号统计
**通俗解释**：就像看哪些号码是"常客"（经常出现），哪些是"稀客"（很少出现）。

**数学定义**：基于统计学原理，计算每个号码在指定期数内的出现频率，并根据标准差确定冷热分类。

**计算方法**：
- **理论期望频率**：`期数 ÷ 35`（前区35个号码）
- **标准差**：`√(期望频率 × (1 - 期望频率/期数))`
- **热号阈值**：`期望频率 + 标准差`
- **冷号阈值**：`期望频率 - 标准差`
- **分类标准**：
  - 热号：出现频率 > 热号阈值
  - 温号：冷号阈值 ≤ 出现频率 ≤ 热号阈值
  - 冷号：出现频率 < 冷号阈值

**代码实现**：
```python
import pandas as pd
import numpy as np

def analyze_hot_cold_numbers(data, periods=100):
    """冷热号统计分析"""
    # 计算每个号码的出现频率
    frequency = data.value_counts()

    # 计算理论期望和标准差
    expected_freq = periods / 35
    std_dev = np.sqrt(expected_freq * (1 - expected_freq/periods))

    # 定义阈值
    hot_threshold = expected_freq + std_dev
    cold_threshold = expected_freq - std_dev

    # 分类
    result = {}
    for num in range(1, 36):
        freq = frequency.get(num, 0)
        if freq > hot_threshold:
            result[num] = '热号'
        elif freq < cold_threshold:
            result[num] = '冷号'
        else:
            result[num] = '温号'

    return result
```

**应用场景**：
- 识别长期活跃的热门号码
- 发现被忽视的冷门号码
- 基于均值回归理论进行选号参考

### 2. 大小号分析
**通俗解释**：把号码分成"小号"（1-17）和"大号"（18-35）两组，看分布是否均匀。

**数学定义**：基于大乐透前区号码范围1-35的数学中位数18作为分界点，统计大小号的分布比例。

**计算方法**：
- **分界标准**：数学中位数 = `(1 + 35) ÷ 2 = 18`
- **小号范围**：1-17（17个号码）
- **大号范围**：18-35（18个号码）
- **大小比**：`大号数量 : 小号数量`

**代码实现**：
```python
def analyze_size_distribution(numbers):
    """大小号分析"""
    median = 18  # 基于1-35的数学中位数

    small_numbers = [n for n in numbers if n < median]
    large_numbers = [n for n in numbers if n >= median]

    return {
        '小号(1-17)': small_numbers,
        '大号(18-35)': large_numbers,
        '小号数量': len(small_numbers),
        '大号数量': len(large_numbers),
        '大小比': f"{len(large_numbers)}:{len(small_numbers)}",
        '分布均衡度': abs(len(large_numbers) - len(small_numbers))
    }
```

**应用场景**：
- 检查号码选择的均衡性
- 避免过度集中在大号或小号区间
- 作为选号策略的约束条件

### 3. 奇偶分析
**通俗解释**：统计奇数（1,3,5...）和偶数（2,4,6...）的比例，看是否平衡。

**数学定义**：基于模运算判断号码的奇偶性，统计奇偶号码的分布比例。

**计算方法**：
- **奇数判断**：`n % 2 = 1`
- **偶数判断**：`n % 2 = 0`
- **奇偶比**：`奇数数量 : 偶数数量`

**代码实现**：
```python
def analyze_odd_even_distribution(numbers):
    """奇偶分析"""
    odd_numbers = [n for n in numbers if n % 2 == 1]
    even_numbers = [n for n in numbers if n % 2 == 0]

    return {
        '奇数': odd_numbers,
        '偶数': even_numbers,
        '奇数数量': len(odd_numbers),
        '偶数数量': len(even_numbers),
        '奇偶比': f"{len(odd_numbers)}:{len(even_numbers)}",
        '奇数占比': len(odd_numbers) / len(numbers) * 100
    }
```

**应用场景**：
- 保持奇偶号码的合理分布
- 避免全奇数或全偶数的极端情况
- 提高号码组合的随机性

### 4. 和值分析
**通俗解释**：把5个前区号码加起来，看总和的分布规律和概率。

**数学定义**：计算前区5个号码的算术和，分析和值的分布特征和概率密度。

**计算方法**：
- **和值计算**：`sum = n1 + n2 + n3 + n4 + n5`
- **理论范围**：最小值15（1+2+3+4+5），最大值155（31+32+33+34+35）
- **期望值**：`E(sum) = 5 × (1+35)/2 = 90`
- **标准差**：基于超几何分布计算

**代码实现**：
```python
import scipy.stats as stats

def analyze_sum_distribution(numbers_list):
    """和值分析"""
    sums = [sum(numbers) for numbers in numbers_list]

    # 统计分析
    mean_sum = np.mean(sums)
    std_sum = np.std(sums)

    # 分布区间
    ranges = {
        '低和值(15-60)': len([s for s in sums if 15 <= s <= 60]),
        '中和值(61-120)': len([s for s in sums if 61 <= s <= 120]),
        '高和值(121-155)': len([s for s in sums if 121 <= s <= 155])
    }

    return {
        '和值列表': sums,
        '平均和值': round(mean_sum, 2),
        '标准差': round(std_sum, 2),
        '理论期望': 90,
        '分布统计': ranges,
        '正态性检验': stats.normaltest(sums)
    }
```

**应用场景**：
- 作为选号的重要约束条件
- 避免和值过大或过小的组合
- 基于概率分布优化选号策略

### 5. 连号分析
**通俗解释**：找连续的号码组合，比如1,2,3或者15,16这样相邻的数字。

**数学定义**：检测开奖号码中是否存在连续的自然数序列，统计连号的出现模式和频率。

**计算方法**：
- **连号定义**：相邻自然数序列，如(n, n+1)或(n, n+1, n+2)
- **检测算法**：对排序后的号码使用差分运算
- **连号类型**：
  - 二连号：2个连续数字
  - 三连号：3个连续数字
  - 多连号：3个以上连续数字

**代码实现**：
```python
def analyze_consecutive_numbers(numbers):
    """连号分析"""
    sorted_numbers = sorted(numbers)
    consecutive_groups = []
    current_group = [sorted_numbers[0]]

    for i in range(1, len(sorted_numbers)):
        if sorted_numbers[i] == sorted_numbers[i-1] + 1:
            current_group.append(sorted_numbers[i])
        else:
            if len(current_group) >= 2:
                consecutive_groups.append(current_group)
            current_group = [sorted_numbers[i]]

    # 检查最后一组
    if len(current_group) >= 2:
        consecutive_groups.append(current_group)

    return {
        '连号组合': consecutive_groups,
        '连号数量': len(consecutive_groups),
        '最长连号': max(consecutive_groups, key=len) if consecutive_groups else [],
        '连号类型': {
            '二连号': len([g for g in consecutive_groups if len(g) == 2]),
            '三连号': len([g for g in consecutive_groups if len(g) == 3]),
            '多连号': len([g for g in consecutive_groups if len(g) > 3])
        }
    }
```

**应用场景**：
- 识别号码的连续性特征
- 避免过多连号导致的风险集中
- 作为号码组合多样性的评估指标

### 6. 间隔分析
**通俗解释**：看号码之间的距离，比如3和15之间隔了12个位置。

**数学定义**：计算排序后相邻号码之间的差值，分析间隔的分布特征和规律性。

**计算方法**：
- **间隔计算**：对排序后的号码计算`diff = numbers[i+1] - numbers[i]`
- **间隔统计**：统计各种间隔值的出现频率
- **间隔特征**：平均间隔、最大间隔、最小间隔

**代码实现**：
```python
def analyze_intervals(numbers):
    """间隔分析"""
    sorted_numbers = sorted(numbers)
    intervals = []

    for i in range(len(sorted_numbers) - 1):
        interval = sorted_numbers[i+1] - sorted_numbers[i]
        intervals.append(interval)

    return {
        '号码序列': sorted_numbers,
        '间隔序列': intervals,
        '平均间隔': round(np.mean(intervals), 2),
        '最大间隔': max(intervals),
        '最小间隔': min(intervals),
        '间隔标准差': round(np.std(intervals), 2),
        '间隔分布': pd.Series(intervals).value_counts().to_dict(),
        '理论平均间隔': 35 / 6  # 35个号码选5个，理论平均间隔
    }
```

**应用场景**：
- 评估号码分布的均匀性
- 避免号码过度集中或分散
- 优化号码选择的空间分布

### 7. 区间分析
**通俗解释**：把1-35分成几个区域（如1-12, 13-24, 25-35），看每个区域出了几个号。

**数学定义**：将前区号码范围1-35按等距原则划分为三个区间，统计各区间的号码分布。

**计算方法**：
- **标准三区划分**：
  - 一区：1-12（12个号码）
  - 二区：13-24（12个号码）
  - 三区：25-35（11个号码）
- **区间比**：`一区数量:二区数量:三区数量`
- **均衡度**：各区间数量的标准差

**代码实现**：
```python
def analyze_zone_distribution(numbers):
    """区间分析"""
    # 标准三区划分
    zone1 = [n for n in numbers if 1 <= n <= 12]
    zone2 = [n for n in numbers if 13 <= n <= 24]
    zone3 = [n for n in numbers if 25 <= n <= 35]

    zone_counts = [len(zone1), len(zone2), len(zone3)]

    return {
        '一区(1-12)': zone1,
        '二区(13-24)': zone2,
        '三区(25-35)': zone3,
        '区间数量': {
            '一区': len(zone1),
            '二区': len(zone2),
            '三区': len(zone3)
        },
        '区间比': f"{len(zone1)}:{len(zone2)}:{len(zone3)}",
        '分布均衡度': round(np.std(zone_counts), 2),
        '理论期望': '1.67:1.67:1.67'  # 5个号码平均分布
    }
```

**应用场景**：
- 确保号码在各区间的合理分布
- 避免某个区间号码过度集中
- 作为选号策略的重要参考指标

### 8. 周期性分析
**通俗解释**：看号码是否有规律性的重复出现，比如某个号码每隔几期就出现一次。

**数学定义**：使用时间序列分析方法检测号码出现的周期性模式，但需要注意彩票的随机性本质。

**计算方法**：
- **自相关分析**：计算时间序列的自相关函数
- **频谱分析**：使用FFT检测主要频率成分
- **显著性检验**：验证周期性的统计显著性

**代码实现**：
```python
from scipy import signal
from scipy.fft import fft

def analyze_periodicity(number_series, significance_level=0.05):
    """周期性分析（谨慎使用）"""
    # 创建二进制时间序列（出现=1，不出现=0）
    binary_series = np.array(number_series)

    # 自相关分析
    autocorr = np.correlate(binary_series, binary_series, mode='full')
    autocorr = autocorr[autocorr.size // 2:]

    # FFT频谱分析
    fft_result = fft(binary_series)
    frequencies = np.fft.fftfreq(len(binary_series))
    power_spectrum = np.abs(fft_result) ** 2

    # 寻找主要周期（谨慎解释）
    peaks, _ = signal.find_peaks(power_spectrum[1:len(power_spectrum)//2])

    return {
        '自相关系数': autocorr[:20].tolist(),  # 前20个滞后期
        '主要频率': frequencies[peaks + 1].tolist(),
        '功率谱': power_spectrum[:20].tolist(),
        '警告': '彩票本质随机，周期性分析仅供参考，不应作为预测依据',
        '建议': '结合其他统计特征使用，避免过度依赖周期性'
    }
```

**应用场景**：
- **谨慎使用**：仅作为辅助分析工具
- 检测可能的伪周期性模式
- **重要提醒**：彩票开奖本质随机，不存在真正的周期性

### 9. 遗漏走势分析
**通俗解释**：统计每个号码多久没出现了，就像记录朋友多久没联系一样。

**数学定义**：统计每个号码的遗漏期数、最大遗漏、平均遗漏，以及走势方向分析。

**计算方法**：
- **当前遗漏期数**：从该号码最后一次出现到当前期的间隔期数
- **最大遗漏期数**：该号码历史上最长的连续未出现期数
- **平均遗漏期数**：该号码所有遗漏期数的平均值
- **走势方向**：基于最近3次出现间隔的变化趋势
  - 上升：最近间隔缩短，出现频率提高
  - 下降：最近间隔增长，出现频率降低
  - 稳定：最近间隔变化不明显

**代码实现**：
```python
def analyze_omission_trend(number, draw_history):
    """遗漏走势分析"""
    appearances = []
    for i, draw in enumerate(draw_history):
        if number in draw:
            appearances.append(i)

    if len(appearances) < 2:
        return {'错误': '数据不足，无法分析'}

    # 计算遗漏期数
    omissions = []
    for i in range(1, len(appearances)):
        omission = appearances[i] - appearances[i-1] - 1
        omissions.append(omission)

    # 当前遗漏
    current_omission = len(draw_history) - appearances[-1] - 1

    # 走势分析
    recent_omissions = omissions[-3:] if len(omissions) >= 3 else omissions
    if len(recent_omissions) >= 2:
        if recent_omissions[-1] < recent_omissions[0]:
            trend = '上升'
        elif recent_omissions[-1] > recent_omissions[0]:
            trend = '下降'
        else:
            trend = '稳定'
    else:
        trend = '数据不足'

    return {
        '当前遗漏': current_omission,
        '最大遗漏': max(omissions) if omissions else 0,
        '平均遗漏': round(np.mean(omissions), 2) if omissions else 0,
        '遗漏列表': omissions,
        '走势方向': trend
    }
```

**应用场景**：
- 识别长期遗漏的"冷门"号码，基于均值回归理论进行选号
- 分析号码的出现规律和周期性特征
- 结合冷热号分析，提供更全面的号码评估

### 10. 跨度分析
**通俗解释**：看最大号码和最小号码之间的差距，就像测量一组数据的"张开程度"。

**数学定义**：计算每期开奖号码中最大值与最小值的差值，分析跨度分布规律。

**计算方法**：
- **跨度值**：`max(开奖号码) - min(开奖号码)`
- **跨度频率**：各跨度值在历史数据中的出现次数
- **跨度概率**：各跨度值的出现概率
- **最优跨度**：出现概率大于5%的跨度值

**代码实现**：
```python
def analyze_span_distribution(numbers_list):
    """跨度分析"""
    spans = []
    for numbers in numbers_list:
        span = max(numbers) - min(numbers)
        spans.append(span)

    # 统计分析
    span_counts = pd.Series(spans).value_counts().sort_index()
    total_draws = len(spans)
    span_probabilities = span_counts / total_draws

    # 最优跨度（概率>5%）
    optimal_spans = span_probabilities[span_probabilities > 0.05].index.tolist()

    return {
        '跨度列表': spans,
        '平均跨度': round(np.mean(spans), 2),
        '跨度范围': f"{min(spans)}-{max(spans)}",
        '跨度频率': span_counts.to_dict(),
        '跨度概率': {k: round(v, 4) for k, v in span_probabilities.items()},
        '最优跨度': optimal_spans,
        '理论范围': '4-30（前区5个号码的理论跨度范围）'
    }
```

**应用场景**：
- 作为选号的约束条件，避免过于集中或过于分散的号码组合
- 前区跨度通常在15-30之间，后区跨度通常在1-10之间
- 结合区间分析，优化号码分布策略

## 特征准确性评估

### A级特征（高准确性 - 数学上绝对可靠）
- **特征3：奇偶分析** - 基于模运算，100%准确
- **特征4：和值分析** - 基于加法运算，100%准确
- **特征9：遗漏走势分析** - 定义明确，95%准确
- **特征10：跨度分析** - 基于max-min运算，100%准确

### B级特征（中等准确性 - 已标准化）
- **特征1：冷热号统计** - 基于统计学标准差，90%准确
- **特征2：大小号分析** - 基于数学中位数，95%准确
- **特征7：区间分析** - 基于等距划分，90%准确

### C级特征（需谨慎使用）
- **特征5：连号分析** - 定义已明确，但实现复杂，85%准确
- **特征6：间隔分析** - 算法已标准化，90%准确
- **特征8：周期性分析** - 存在理论争议，仅供参考，60%可信度

## 使用建议

### 推荐使用顺序
1. **优先使用A级特征**：作为核心分析工具
2. **合理使用B级特征**：作为重要补充
3. **谨慎使用C级特征**：仅作为辅助参考

### 数据验证要求
- 所有特征计算前必须验证数据完整性
- 使用pandas的数据类型检查和异常处理
- 对计算结果进行合理性检验

### 准确性保证措施
- 使用经过验证的Python科学计算库
- 实施标准化的数学定义和算法
- 提供详细的计算过程和中间结果
- 建立完整的测试用例和验证机制

## 项目目标
通过科学严谨的分析验证，客观评估大乐透预测的可能性边界，为理性购彩提供数据支持。