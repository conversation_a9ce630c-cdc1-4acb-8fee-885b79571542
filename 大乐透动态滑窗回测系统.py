#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大乐透动态滑窗回测系统
严格按照动态训练集逻辑：每预测一期后，训练集向前滑动一期
完全模拟真实预测环境，绝对禁止使用未来数据
"""

import pandas as pd
import numpy as np
import sqlite3
import warnings
warnings.filterwarnings('ignore')

class LotteryDynamicSlidingBacktest:
    def __init__(self):
        self.data = None
        self.methods = {
            'entropy': '信息熵方法',
            'mutual_info': '互信息方法', 
            'conditional_entropy': '条件熵方法',
            'frequency': '频率统计方法',
            'pattern': '模式识别方法'
        }
        # 动态滑窗回测结果
        self.dynamic_results = {}
        
    def load_real_data(self):
        """加载真实大乐透历史数据"""
        print("🔄 从数据库加载真实历史数据...")
        
        try:
            conn = sqlite3.connect('lottery.db')
            query = """
            SELECT period, draw_date, red_balls, blue_balls 
            FROM lottery_data 
            ORDER BY CAST(period AS INTEGER)
            """
            df = pd.read_sql_query(query, conn)
            conn.close()
            
            if df.empty:
                print("❌ 数据库中没有找到数据")
                return False
            
            print(f"✅ 已加载 {len(df)} 期真实历史数据")
            print(f"📅 数据范围：第{df['period'].iloc[0]}期 到 第{df['period'].iloc[-1]}期")
            
            # 解析红球和蓝球数据
            processed_data = []
            for _, row in df.iterrows():
                try:
                    red_balls = [int(x.strip()) for x in row['red_balls'].split(',')]
                    blue_balls = [int(x.strip()) for x in row['blue_balls'].split(',')]

                    if len(red_balls) == 5 and len(blue_balls) == 2:
                        data_row = {'period': row['period'], 'date': row['draw_date']}
                        for i, ball in enumerate(red_balls, 1):
                            data_row[f'front_{i}'] = ball
                        for i, ball in enumerate(blue_balls, 1):
                            data_row[f'back_{i}'] = ball
                        processed_data.append(data_row)
                except Exception:
                    continue
            
            self.data = pd.DataFrame(processed_data)
            print(f"\n✅ 数据处理完成，共{len(self.data)}期有效数据")
            print(f"📊 测试范围: 最新20期（第{self.data['period'].iloc[-20]}期 到 第{self.data['period'].iloc[-1]}期）")
            return True
            
        except Exception as e:
            print(f"❌ 加载数据失败: {e}")
            return False
    
    def calculate_coverage_rate(self, candidates, actual):
        """计算覆盖率"""
        if not candidates or not actual:
            return 0.0
        return len(set(candidates) & set(actual)) / len(actual)

    def _get_numbers_from_data(self, train_data, cols):
        """从训练数据中提取号码列表"""
        numbers = []
        for col in cols:
            numbers.extend(train_data[col].tolist())
        return numbers

    def _get_actual_numbers(self, test_data):
        """提取实际开奖号码"""
        actual_front = [test_data[f'front_{i}'] for i in range(1, 6)]
        actual_back = [test_data[f'back_{i}'] for i in range(1, 3)]
        return actual_front, actual_back

    def _calculate_statistics(self, coverages):
        """计算统计指标"""
        if not coverages:
            return 0, 0, 0, 0, 0
        return (np.mean(coverages), np.max(coverages), np.min(coverages),
                np.std(coverages), np.mean([c > 0 for c in coverages]) * 100)
    
    def entropy_method_predict(self, train_data):
        """信息熵方法预测 - 冷号回归策略，选择历史出现频率最低的号码"""
        try:
            # 检查训练数据是否为空
            if train_data.empty:
                raise ValueError("训练数据为空")

            # 前区预测
            front_numbers = self._get_numbers_from_data(train_data, ['front_1', 'front_2', 'front_3', 'front_4', 'front_5'])
            if not front_numbers:
                raise ValueError("前区数据为空")

            front_counts = pd.Series(front_numbers).value_counts()
            total_front = len(front_numbers)

            front_entropy = {}
            for num in range(1, 36):
                if num in front_counts:
                    prob = front_counts[num] / total_front
                    # 信息熵公式：H = -p * log2(p)
                    # 熵值越低表示该号码历史出现频率越低
                    entropy_score = -prob * np.log2(prob)
                else:
                    # 未出现的号码设置为最高熵值，表示最高不确定性
                    entropy_score = 10
                front_entropy[num] = entropy_score

            # 选择熵值最低的号码（历史出现频率最低的冷号）
            front_candidates = sorted(front_entropy.keys(), key=lambda x: front_entropy[x])[:10]

            # 后区预测
            back_numbers = self._get_numbers_from_data(train_data, ['back_1', 'back_2'])
            if not back_numbers:
                raise ValueError("后区数据为空")

            back_counts = pd.Series(back_numbers).value_counts()
            total_back = len(back_numbers)

            back_entropy = {}
            for num in range(1, 13):
                if num in back_counts:
                    prob = back_counts[num] / total_back
                    entropy_score = -prob * np.log2(prob)
                else:
                    entropy_score = 10
                back_entropy[num] = entropy_score

            back_candidates = sorted(back_entropy.keys(), key=lambda x: back_entropy[x])[:5]

            return front_candidates, back_candidates

        except (ValueError, KeyError, ZeroDivisionError) as e:
            # 对于可预期的错误，返回随机候选号码
            np.random.seed(42)  # 固定随机种子确保可重现
            front_random = sorted(np.random.choice(range(1, 36), 12, replace=False).tolist())
            back_random = sorted(np.random.choice(range(1, 13), 5, replace=False).tolist())
            return front_random, back_random
        except Exception as e:
            # 对于未知错误，抛出异常以便调试
            raise RuntimeError(f"信息熵方法预测失败: {str(e)}")
    
    def mutual_info_method_predict(self, train_data):
        """互信息方法预测 - 基于号码间的真实互信息相关性"""
        try:
            # 前区预测
            front_cols = ['front_1', 'front_2', 'front_3', 'front_4', 'front_5']

            # 计算边际概率
            all_front_numbers = self._get_numbers_from_data(train_data, front_cols)
            if not all_front_numbers:
                raise ValueError("前区数据为空")

            front_counts = pd.Series(all_front_numbers).value_counts()
            total_front = len(all_front_numbers)
            marginal_probs = {num: count/total_front for num, count in front_counts.items()}

            # 计算联合概率
            cooccurrence = {}
            total_periods = len(train_data)

            for _, row in train_data.iterrows():
                front_nums = [row[col] for col in front_cols]
                for i, num1 in enumerate(front_nums):
                    for j, num2 in enumerate(front_nums):
                        if i != j:
                            key = (min(num1, num2), max(num1, num2))
                            cooccurrence[key] = cooccurrence.get(key, 0) + 1

            joint_probs = {pair: count/total_periods for pair, count in cooccurrence.items()}

            # 计算互信息得分
            front_scores = {}
            for num in range(1, 36):
                if num not in marginal_probs:
                    front_scores[num] = 0
                    continue

                mutual_info_score = 0
                p_x = marginal_probs[num]

                for (n1, n2), p_xy in joint_probs.items():
                    if n1 == num:
                        other_num = n2
                    elif n2 == num:
                        other_num = n1
                    else:
                        continue

                    if other_num in marginal_probs:
                        p_y = marginal_probs[other_num]
                        if p_xy > 0 and p_x > 0 and p_y > 0:
                            mutual_info_score += p_xy * np.log2(p_xy / (p_x * p_y))

                front_scores[num] = mutual_info_score

            front_candidates = sorted(front_scores.keys(), key=lambda x: front_scores[x], reverse=True)[:10]

            # 后区预测
            back_numbers = self._get_numbers_from_data(train_data, ['back_1', 'back_2'])
            if not back_numbers:
                raise ValueError("后区数据为空")

            back_counts = pd.Series(back_numbers).value_counts()
            total_back = len(back_numbers)
            back_marginal_probs = {num: count/total_back for num, count in back_counts.items()}

            back_cooccurrence = {}
            for _, row in train_data.iterrows():
                back_nums = [row['back_1'], row['back_2']]
                key = (min(back_nums), max(back_nums))
                back_cooccurrence[key] = back_cooccurrence.get(key, 0) + 1

            back_joint_probs = {pair: count/total_periods for pair, count in back_cooccurrence.items()}

            back_scores = {}
            for num in range(1, 13):
                if num not in back_marginal_probs:
                    back_scores[num] = 0
                    continue

                mutual_info_score = 0
                p_x = back_marginal_probs[num]

                for (n1, n2), p_xy in back_joint_probs.items():
                    if n1 == num:
                        other_num = n2
                    elif n2 == num:
                        other_num = n1
                    else:
                        continue

                    if other_num in back_marginal_probs:
                        p_y = back_marginal_probs[other_num]
                        if p_xy > 0 and p_x > 0 and p_y > 0:
                            mutual_info_score += p_xy * np.log2(p_xy / (p_x * p_y))

                back_scores[num] = mutual_info_score

            back_candidates = sorted(back_scores.keys(), key=lambda x: back_scores[x], reverse=True)[:5]
            return front_candidates, back_candidates

        except (ValueError, KeyError, ZeroDivisionError) as e:
            np.random.seed(42)
            front_random = sorted(np.random.choice(range(1, 36), 10, replace=False).tolist())
            back_random = sorted(np.random.choice(range(1, 13), 5, replace=False).tolist())
            return front_random, back_random
        except Exception as e:
            raise RuntimeError(f"互信息方法预测失败: {str(e)}")
    
    def conditional_entropy_method_predict(self, train_data):
        """条件熵方法预测 - 基于位置条件熵分析"""
        try:
            # 检查训练数据是否为空
            if train_data.empty:
                raise ValueError("训练数据为空")

            # 前区预测
            front_cols = ['front_1', 'front_2', 'front_3', 'front_4', 'front_5']

            # 计算每个位置的号码分布概率
            position_probs = {}
            for i, col in enumerate(front_cols):
                position_probs[i] = train_data[col].value_counts(normalize=True).to_dict()

            # 计算每个号码的条件熵得分
            front_scores = {}
            for num in range(1, 36):
                total_conditional_entropy = 0

                # 对每个位置计算条件熵：H(X|Y=position)
                for pos_idx in range(5):
                    prob_at_position = position_probs[pos_idx].get(num, 0)
                    if prob_at_position > 0:
                        # 条件熵公式：-p(x|y) * log2(p(x|y))
                        conditional_entropy = -prob_at_position * np.log2(prob_at_position)
                        total_conditional_entropy += conditional_entropy

                front_scores[num] = total_conditional_entropy

            front_candidates = sorted(front_scores.keys(), key=lambda x: front_scores[x], reverse=True)[:10]

            # 后区预测
            back_cols = ['back_1', 'back_2']
            back_position_probs = {}
            for i, col in enumerate(back_cols):
                back_position_probs[i] = train_data[col].value_counts(normalize=True).to_dict()

            back_scores = {}
            for num in range(1, 13):
                total_conditional_entropy = 0

                for pos_idx in range(2):
                    prob_at_position = back_position_probs[pos_idx].get(num, 0)
                    if prob_at_position > 0:
                        conditional_entropy = -prob_at_position * np.log2(prob_at_position)
                        total_conditional_entropy += conditional_entropy

                back_scores[num] = total_conditional_entropy

            back_candidates = sorted(back_scores.keys(), key=lambda x: back_scores[x], reverse=True)[:5]
            return front_candidates, back_candidates

        except (ValueError, KeyError, ZeroDivisionError) as e:
            np.random.seed(42)
            front_random = sorted(np.random.choice(range(1, 36), 10, replace=False).tolist())
            back_random = sorted(np.random.choice(range(1, 13), 5, replace=False).tolist())
            return front_random, back_random
        except Exception as e:
            raise RuntimeError(f"条件熵方法预测失败: {str(e)}")
    
    def frequency_method_predict(self, train_data):
        """频率统计方法预测 - 选择最热门号码"""
        try:
            # 检查训练数据是否为空
            if train_data.empty:
                raise ValueError("训练数据为空")

            # 前区预测
            front_numbers = self._get_numbers_from_data(train_data, ['front_1', 'front_2', 'front_3', 'front_4', 'front_5'])
            if not front_numbers:
                raise ValueError("前区数据为空")

            front_counts = pd.Series(front_numbers).value_counts()
            front_candidates = front_counts.head(10).index.tolist()

            # 后区预测
            back_numbers = self._get_numbers_from_data(train_data, ['back_1', 'back_2'])
            if not back_numbers:
                raise ValueError("后区数据为空")

            back_counts = pd.Series(back_numbers).value_counts()
            back_candidates = back_counts.head(5).index.tolist()

            return front_candidates, back_candidates

        except (ValueError, KeyError, ZeroDivisionError) as e:
            np.random.seed(42)
            front_random = sorted(np.random.choice(range(1, 36), 10, replace=False).tolist())
            back_random = sorted(np.random.choice(range(1, 13), 5, replace=False).tolist())
            return front_random, back_random
        except Exception as e:
            raise RuntimeError(f"频率统计方法预测失败: {str(e)}")
    
    def pattern_method_predict(self, train_data):
        """模式识别方法预测 - 基于趋势和模式"""
        try:
            # 检查训练数据是否为空
            if train_data.empty:
                raise ValueError("训练数据为空")

            # 前区预测
            front_cols = ['front_1', 'front_2', 'front_3', 'front_4', 'front_5']
            recent_weight = {}
            total_periods = len(train_data)

            if total_periods == 0:
                raise ValueError("训练期数为零")

            for idx, (_, row) in enumerate(train_data.iterrows()):
                weight = (idx + 1) / total_periods
                for col in front_cols:
                    num = row[col]
                    recent_weight[num] = recent_weight.get(num, 0) + weight

            front_numbers = self._get_numbers_from_data(train_data, front_cols)
            if not front_numbers:
                raise ValueError("前区数据为空")

            front_counts = pd.Series(front_numbers).value_counts()

            front_scores = {}
            for num in range(1, 36):
                freq_score = front_counts.get(num, 0)
                trend_score = recent_weight.get(num, 0)
                front_scores[num] = freq_score + trend_score * 2

            front_candidates = sorted(front_scores.keys(), key=lambda x: front_scores[x], reverse=True)[:10]

            # 后区预测
            back_cols = ['back_1', 'back_2']
            back_recent_weight = {}
            for idx, (_, row) in enumerate(train_data.iterrows()):
                weight = (idx + 1) / total_periods
                for col in back_cols:
                    num = row[col]
                    back_recent_weight[num] = back_recent_weight.get(num, 0) + weight

            back_numbers = self._get_numbers_from_data(train_data, back_cols)
            if not back_numbers:
                raise ValueError("后区数据为空")

            back_counts = pd.Series(back_numbers).value_counts()

            back_scores = {}
            for num in range(1, 13):
                freq_score = back_counts.get(num, 0)
                trend_score = back_recent_weight.get(num, 0)
                back_scores[num] = freq_score + trend_score * 2

            back_candidates = sorted(back_scores.keys(), key=lambda x: back_scores[x], reverse=True)[:5]
            return front_candidates, back_candidates

        except (ValueError, KeyError, ZeroDivisionError) as e:
            np.random.seed(42)
            front_random = sorted(np.random.choice(range(1, 36), 10, replace=False).tolist())
            back_random = sorted(np.random.choice(range(1, 13), 5, replace=False).tolist())
            return front_random, back_random
        except Exception as e:
            raise RuntimeError(f"模式识别方法预测失败: {str(e)}")

    def run_dynamic_sliding_backtest(self, min_train_periods=10, max_train_periods=100, test_periods=20):
        """运行动态滑窗回测 - 真正模拟实际预测环境"""
        print("\n" + "="*80)
        print("🎯 大乐透动态滑窗回测系统")
        print("="*80)
        print("🔄 动态训练集逻辑：每预测一期后，训练集向前滑动一期")
        print("⚠️ 严格时序约束：绝对禁止使用未来开奖数据")
        print(f"📊 回测参数：")
        print(f"   训练期数范围: {min_train_periods}-{max_train_periods}期")
        print(f"   测试期数: {test_periods}期")
        print(f"   总数据期数: {len(self.data)}")

        # 确定测试期数范围（最新20期）
        total_periods = len(self.data)
        test_start_idx = total_periods - test_periods
        test_end_idx = total_periods - 1

        test_start_period = self.data.iloc[test_start_idx]['period']
        test_end_period = self.data.iloc[test_end_idx]['period']

        print(f"\n🔍 测试期数范围: 第{test_start_period}期 到 第{test_end_period}期")
        print(f"📈 动态滑窗原理: 训练集随每期预测向前滑动，完全模拟真实环境")

        # 初始化结果存储
        for train_periods in range(min_train_periods, max_train_periods + 1):
            self.dynamic_results[train_periods] = {}
            for method in self.methods:
                self.dynamic_results[train_periods][method] = {
                    'front_coverage': [],
                    'back_coverage': [],
                    'total_coverage': [],
                    'predictions': []
                }

        # 开始动态滑窗回测
        for train_periods in range(min_train_periods, min(max_train_periods + 1, test_start_idx + 1)):
            print(f"\n" + "="*60)
            print(f"🔄 训练期数: {train_periods}期 - 动态滑窗回测")
            print("="*60)

            # 检查是否有足够的历史数据
            if test_start_idx < train_periods:
                print(f"   ⚠️ 跳过：历史数据不足（需要{train_periods}期训练数据）")
                continue

            # 逐期进行动态预测
            for test_step in range(test_periods):
                test_idx = test_start_idx + test_step
                test_data = self.data.iloc[test_idx]
                test_period = test_data['period']

                # 动态确定训练集范围（关键：训练集随测试进行而滑动）
                train_end_idx = test_idx - 1  # 训练集结束于测试期的前一期
                train_start_idx = train_end_idx - train_periods + 1  # 训练集开始位置

                # 获取动态训练数据（绝对不包含当前测试期及未来数据）
                train_data = self.data.iloc[train_start_idx:train_end_idx + 1].copy()

                # 获取实际开奖结果
                actual_front, actual_back = self._get_actual_numbers(test_data)

                # 显示详细信息（前3期）
                if test_step < 3:
                    train_start_period = train_data.iloc[0]['period']
                    train_end_period = train_data.iloc[-1]['period']
                    print(f"   📅 第{test_step+1}步: 预测第{test_period}期")
                    print(f"   📈 动态训练集: 第{train_start_period}期-第{train_end_period}期 (共{train_periods}期)")
                    print(f"   🎯 实际开奖: 前区{actual_front} 后区{actual_back}")
                elif test_step == 3:
                    print(f"   📅 继续预测第{test_period}期及后续期数...")

                # 测试每种方法
                for method_name, method_desc in self.methods.items():
                    try:
                        # 调用对应的预测方法
                        predict_func = getattr(self, f"{method_name}_method_predict")
                        front_candidates, back_candidates = predict_func(train_data)

                        # 计算覆盖率
                        front_coverage = self.calculate_coverage_rate(front_candidates, actual_front)
                        back_coverage = self.calculate_coverage_rate(back_candidates, actual_back)
                        total_coverage = (front_coverage * 5 + back_coverage * 2) / 7  # 加权平均

                        # 存储结果
                        self.dynamic_results[train_periods][method_name]['front_coverage'].append(front_coverage)
                        self.dynamic_results[train_periods][method_name]['back_coverage'].append(back_coverage)
                        self.dynamic_results[train_periods][method_name]['total_coverage'].append(total_coverage)
                        self.dynamic_results[train_periods][method_name]['predictions'].append({
                            'period': test_period,
                            'train_periods': train_periods,
                            'train_start_period': train_data.iloc[0]['period'],
                            'train_end_period': train_data.iloc[-1]['period'],
                            'front_candidates': front_candidates,
                            'back_candidates': back_candidates,
                            'actual_front': actual_front,
                            'actual_back': actual_back,
                            'front_coverage': front_coverage,
                            'back_coverage': back_coverage,
                            'total_coverage': total_coverage
                        })

                        # 显示前3期的详细结果
                        if test_step < 3:
                            print(f"      {method_desc}: 前区{front_coverage:.1%} 后区{back_coverage:.1%} 总{total_coverage:.1%}")

                    except Exception as e:
                        if test_step < 3:
                            print(f"      ❌ {method_desc} 预测失败: {str(e)}")

            # 显示当前训练期数的汇总结果
            print(f"\n   📊 {train_periods}期动态滑窗回测汇总:")
            for method_name, method_desc in self.methods.items():
                coverages = self.dynamic_results[train_periods][method_name]['total_coverage']
                if coverages:
                    avg, max_val, min_val, std, success_rate = self._calculate_statistics(coverages)
                    print(f"      {method_desc}: 平均{avg:.1%} 最高{max_val:.1%} 最低{min_val:.1%} 成功率{success_rate:.0f}%")
                else:
                    print(f"      {method_desc}: 无有效结果")

        # 输出最终分析结果
        self.print_dynamic_backtest_results()

        return self.dynamic_results

    def print_dynamic_backtest_results(self):
        """打印动态滑窗回测结果分析"""
        print("\n" + "="*80)
        print("📊 动态滑窗回测结果分析报告")
        print("="*80)
        print("🔄 本报告基于动态训练集回测，完全模拟真实预测环境")

        # 计算每种方法在不同训练期数下的最佳表现
        method_best_performance = {}

        for method_name, method_desc in self.methods.items():
            method_best_performance[method_name] = {
                'desc': method_desc,
                'best_train_periods': None,
                'best_avg_coverage': 0,
                'all_performances': {}
            }

            # 遍历所有训练期数
            for train_periods, results in self.dynamic_results.items():
                if method_name in results and results[method_name]['total_coverage']:
                    coverages = results[method_name]['total_coverage']
                    avg, max_val, min_val, std, success_rate = self._calculate_statistics(coverages)

                    method_best_performance[method_name]['all_performances'][train_periods] = {
                        'avg_coverage': avg,
                        'max_coverage': max_val,
                        'min_coverage': min_val,
                        'std_coverage': std,
                        'test_count': len(coverages),
                        'success_rate': success_rate
                    }

                    # 更新最佳表现
                    if avg > method_best_performance[method_name]['best_avg_coverage']:
                        method_best_performance[method_name]['best_avg_coverage'] = avg
                        method_best_performance[method_name]['best_train_periods'] = train_periods

        # 按最佳平均覆盖率排序
        sorted_methods = sorted(method_best_performance.items(),
                              key=lambda x: x[1]['best_avg_coverage'], reverse=True)

        print("\n🏆 各方法最佳表现排名（动态滑窗环境下）：")
        print("-" * 60)
        for i, (method_name, stats) in enumerate(sorted_methods, 1):
            if stats['best_train_periods'] is not None:
                print(f"{i}. {stats['desc']}")
                print(f"   🏆 最佳平均覆盖率: {stats['best_avg_coverage']:.1%}")
                print(f"   🎯 最佳训练期数: {stats['best_train_periods']}期")
                best_perf = stats['all_performances'][stats['best_train_periods']]
                print(f"   📊 测试次数: {best_perf['test_count']}")
                print(f"   ✅ 成功率: {best_perf['success_rate']:.1f}%")
                print(f"   📈 最高覆盖率: {best_perf['max_coverage']:.1%}")
                print(f"   📉 最低覆盖率: {best_perf['min_coverage']:.1%}")
                print(f"   📊 稳定性: {best_perf['std_coverage']:.3f} (标准差)")
                print()

        print("🔍 训练期数效应分析（动态滑窗环境）：")
        print("-" * 60)

        # 分析训练期数的整体趋势
        train_periods_summary = {}
        for train_periods in sorted(self.dynamic_results.keys()):
            train_periods_summary[train_periods] = []
            for method_name in self.methods.keys():
                if (train_periods in self.dynamic_results and
                    method_name in self.dynamic_results[train_periods] and
                    self.dynamic_results[train_periods][method_name]['total_coverage']):
                    avg_coverage = np.mean(self.dynamic_results[train_periods][method_name]['total_coverage'])
                    train_periods_summary[train_periods].append(avg_coverage)

        print("📈 不同训练期数的整体表现 (前20名):")
        valid_periods = [(p, coverages) for p, coverages in train_periods_summary.items() if coverages]
        valid_periods.sort(key=lambda x: np.mean(x[1]), reverse=True)

        for i, (periods, coverages) in enumerate(valid_periods[:20], 1):
            avg_all_methods = np.mean(coverages)
            std_all_methods = np.std(coverages)
            print(f"   第{i}名: {periods}期训练 - 平均覆盖率 {avg_all_methods:.1%} (±{std_all_methods:.1%}) 基于{len(coverages)}种方法")

        print("\n💡 动态滑窗回测结论与建议：")
        print("-" * 60)
        if sorted_methods and sorted_methods[0][1]['best_train_periods'] is not None:
            best_method = sorted_methods[0]
            best_perf = best_method[1]['all_performances'][best_method[1]['best_train_periods']]
            print(f"🏆 最佳方法: {best_method[1]['desc']}")
            print(f"   最佳平均覆盖率: {best_method[1]['best_avg_coverage']:.1%}")
            print(f"   推荐训练期数: {best_method[1]['best_train_periods']}期")
            print(f"   稳定性评估: 标准差{best_perf['std_coverage']:.3f}")
            print(f"   实用性评估: 成功率{best_perf['success_rate']:.1f}%")

            if len(sorted_methods) > 1 and sorted_methods[1][1]['best_train_periods'] is not None:
                second_method = sorted_methods[1]
                print(f"\n🥈 次佳方法: {second_method[1]['desc']}")
                print(f"   最佳平均覆盖率: {second_method[1]['best_avg_coverage']:.1%}")
                print(f"   推荐训练期数: {second_method[1]['best_train_periods']}期")

        if valid_periods:
            best_train_periods = valid_periods[0][0]
            best_overall_coverage = np.mean(valid_periods[0][1])
            print(f"\n🎯 整体最佳训练期数: {best_train_periods}期")
            print(f"   所有方法平均覆盖率: {best_overall_coverage:.1%}")

        print("\n⚠️ 重要提醒:")
        print("   • 本回测采用动态滑窗技术，完全模拟真实预测环境")
        print("   • 彩票具有随机性，历史表现不保证未来效果")
        print("   • 请理性购彩，仅供参考")

if __name__ == "__main__":
    import sys
    import codecs
    if sys.platform.startswith('win'):
        sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')
        sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer, 'strict')

    print("🎯 大乐透动态滑窗回测系统启动")

    backtest_system = LotteryDynamicSlidingBacktest()

    if backtest_system.load_real_data():
        results = backtest_system.run_dynamic_sliding_backtest(
            min_train_periods=10,
            max_train_periods=100,
            test_periods=50
        )
        print("\n🎉 动态滑窗回测完成！")
    else:
        print("❌ 数据加载失败，无法进行回测")
